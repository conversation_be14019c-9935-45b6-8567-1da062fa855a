# Getting Started Guide

## Overview

This guide will help you set up and run the Layer 2 Blockchain Security Research Framework for educational and legitimate security research purposes.

## Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows 10/11
- **Rust**: Version 1.70 or higher
- **Memory**: Minimum 8GB RAM recommended
- **Storage**: At least 2GB free disk space

### Required Dependencies
- **Git**: For cloning the repository
- **Cargo**: Rust package manager (included with Rust)
- **Tokio**: Async runtime (automatically installed)

## Installation

### Step 1: Install Rust
If you haven't installed Rust yet, use the official installer:

```bash
# On Unix-like systems (Linux/macOS)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# On Windows, download from: https://rustup.rs/
```

Restart your terminal and verify installation:
```bash
rustc --version
cargo --version
```

### Step 2: Clone the Repository
```bash
git clone https://github.com/security-research/l2-framework
cd l2-framework/src/blockchain/vulnerabilities/l2
```

### Step 3: Build the Framework
```bash
# Build in release mode for optimal performance
cargo build --release

# Or build in debug mode for development
cargo build
```

### Step 4: Verify Installation
```bash
# Run basic framework tests
cargo test

# Check that all modules compile correctly
cargo check
```

## Basic Usage

### Running a Complete Security Analysis

The simplest way to start is by running a comprehensive analysis across all supported Layer 2 networks:

```rust
// main.rs
use l2_security_framework::*;

#[tokio::main]
async fn main() -> Result<()> {
    // Launch comprehensive L2 security research
    launch_l2_security_research().await
}
```

Run the analysis:
```bash
cargo run --release
```

### Understanding the Output

The framework will execute security analysis in five phases:

1. **🔥 Blast Network Analysis**
   - Yield mechanism vulnerability assessment
   - Timing attack vector analysis
   - Points system security evaluation

2. **🏔️ Mantle Network Analysis**
   - Governance security patterns
   - EigenDA integration assessment
   - Token economics security review

3. **📜 Scroll Network Analysis**
   - zkEVM circuit security evaluation
   - Prover coordination analysis
   - Zero-knowledge proof validation

4. **🌐 Cross-L2 Analysis**
   - Bridge vulnerability assessment
   - Cross-chain arbitrage evaluation
   - Systemic risk identification

5. **📊 Comprehensive Reporting**
   - Consolidated security findings
   - Risk assessment summaries
   - Educational recommendations

## Running Specific Network Analysis

### Blast Network Only
```rust
use l2_security_framework::*;

#[tokio::main]
async fn main() -> Result<()> {
    let orchestrator = L2SecurityOrchestrator::new();
    
    // Analyze only Blast network
    orchestrator.analyze_blast_network().await?;
    Ok(())
}
```

### Mantle Network Only
```rust
use l2_security_framework::*;

#[tokio::main]
async fn main() -> Result<()> {
    let orchestrator = L2SecurityOrchestrator::new();
    
    // Analyze only Mantle network
    orchestrator.analyze_mantle_network().await?;
    Ok(())
}
```

### Scroll Network Only
```rust
use l2_security_framework::*;

#[tokio::main]
async fn main() -> Result<()> {
    let orchestrator = L2SecurityOrchestrator::new();
    
    // Analyze only Scroll network
    orchestrator.analyze_scroll_network().await?;
    Ok(())
}
```

## Configuration Options

### Basic Configuration
Create a `config.toml` file in your project root:

```toml
[framework]
analysis_depth = "comprehensive"  # Options: basic, standard, comprehensive
output_format = "detailed"        # Options: summary, standard, detailed
logging_level = "info"            # Options: error, warn, info, debug, trace

[blast]
yield_analysis = true
timing_analysis = true
points_analysis = true

[mantle]
governance_analysis = true
opstack_analysis = true
token_analysis = true

[scroll]
zkevm_analysis = true
prover_analysis = true

[cross_chain]
bridge_analysis = true
vampire_analysis = true
```

### Advanced Configuration
```rust
// Custom configuration example
use l2_security_framework::*;

#[tokio::main]
async fn main() -> Result<()> {
    // Create custom configurations
    let blast_config = BlastConfig {
        yield_rate_threshold: 0.05,
        timing_window_ms: 1000,
        points_multiplier: 2.0,
        max_analysis_depth: 10,
    };
    
    let blast_analyzer = BlastYieldExploiter::new(blast_config);
    
    // Run custom analysis
    let attack_id = "custom_blast_analysis".to_string();
    let results = blast_analyzer.execute_yield_overflow_attack(attack_id).await?;
    
    println!("Custom analysis results: {:?}", results);
    Ok(())
}
```

## Educational Use Cases

### Academic Research Example
```rust
// Academic security research example
use l2_security_framework::*;

#[tokio::main]
async fn main() -> Result<()> {
    println!("🎓 ACADEMIC SECURITY RESEARCH SESSION");
    println!("Purpose: Educational vulnerability assessment");
    
    let orchestrator = L2SecurityOrchestrator::new();
    
    // Document research methodology
    println!("📋 Research Methodology:");
    println!("1. Theoretical vulnerability analysis");
    println!("2. Controlled testing environment");
    println!("3. Educational pattern documentation");
    println!("4. Defensive security assessment");
    
    // Execute educational analysis
    orchestrator.launch_comprehensive_security_analysis().await?;
    
    println!("✅ Academic research session complete");
    println!("📚 Results available for educational review");
    
    Ok(())
}
```

### Security Assessment Training
```rust
// Security training example
use l2_security_framework::*;

#[tokio::main]
async fn main() -> Result<()> {
    println!("🛡️ SECURITY ASSESSMENT TRAINING");
    
    // Step-by-step educational analysis
    let orchestrator = L2SecurityOrchestrator::new();
    
    // Training Phase 1: Understanding vulnerabilities
    println!("📖 Phase 1: Learning vulnerability patterns");
    orchestrator.analyze_blast_network().await?;
    
    // Training Phase 2: Governance security
    println!("🏛️ Phase 2: Governance security analysis");
    orchestrator.analyze_mantle_network().await?;
    
    // Training Phase 3: Cryptographic security
    println!("🔐 Phase 3: Cryptographic protocol analysis");
    orchestrator.analyze_scroll_network().await?;
    
    // Training Phase 4: Cross-chain risks
    println!("🌉 Phase 4: Cross-chain security assessment");
    orchestrator.analyze_cross_l2_vectors().await?;
    
    println!("🎯 Security training complete!");
    Ok(())
}
```

## Data Output and Logging

### Log Files
The framework generates comprehensive logs in the [`logs/`](logs/) directory:
- `security_analysis.log`: Main analysis log
- `vulnerability_findings.log`: Specific vulnerability reports
- `performance_metrics.log`: Analysis performance data

### Data Files
Analysis results are stored in the [`data/`](data/) directory:
- `workflow.db`: SQLite database with structured results
- `vulnerability_reports/`: Individual vulnerability assessments
- `network_analysis/`: Network-specific analysis data

### Report Generation
```rust
// Generate custom reports
use l2_security_framework::*;

#[tokio::main]
async fn main() -> Result<()> {
    let orchestrator = L2SecurityOrchestrator::new();
    
    // Run analysis
    orchestrator.launch_comprehensive_security_analysis().await?;
    
    // Generate educational report
    let report = SecurityReport::new()
        .include_methodology()
        .include_findings()
        .include_recommendations()
        .format_for_education();
    
    report.save_to_file("educational_security_report.md").await?;
    
    Ok(())
}
```

## Troubleshooting

### Common Issues

#### Build Errors
```bash
# Clear cargo cache and rebuild
cargo clean
cargo build --release

# Update Rust if needed
rustup update
```

#### Memory Issues
```bash
# Increase stack size for complex analysis
export RUST_MIN_STACK=8388608
cargo run --release
```

#### Permission Errors
```bash
# Ensure proper file permissions
chmod +x target/release/l2-framework
```

### Environment Variables
```bash
# Set logging level
export RUST_LOG=info

# Set custom data directory
export L2_FRAMEWORK_DATA_DIR=/path/to/data

# Enable detailed analysis
export L2_FRAMEWORK_ANALYSIS_MODE=comprehensive
```

## Best Practices

### Ethical Research Guidelines
1. **Educational Purpose**: Use framework only for learning and legitimate research
2. **Controlled Environment**: Test only in authorized environments
3. **Responsible Disclosure**: Report findings through proper channels
4. **Documentation**: Maintain detailed research documentation
5. **Legal Compliance**: Ensure all activities comply with applicable laws

### Performance Optimization
```rust
// Optimize for large-scale analysis
use l2_security_framework::*;

#[tokio::main]
async fn main() -> Result<()> {
    // Configure for performance
    let config = AnalysisConfig {
        parallel_execution: true,
        batch_size: 1000,
        memory_limit: 4096, // MB
        timeout_seconds: 3600,
    };
    
    let orchestrator = L2SecurityOrchestrator::with_config(config);
    orchestrator.launch_comprehensive_security_analysis().await?;
    
    Ok(())
}
```

### Security Considerations
- Never run analysis on production networks without explicit permission
- Use VPNs or isolated networks for research activities
- Keep research findings confidential until proper disclosure
- Regularly update the framework to include latest security patches

## Next Steps

After completing this setup guide:

1. **Review Security Considerations**: Read [`docs/security-considerations.md`](docs/security-considerations.md)
2. **Explore Educational Materials**: Check [`docs/educational-materials.md`](docs/educational-materials.md)
3. **API Documentation**: Study [`docs/api-documentation.md`](docs/api-documentation.md)
4. **Contribute**: See [`docs/contributing.md`](docs/contributing.md) for contribution guidelines

## Support

### Community Resources
- **Documentation**: Complete framework documentation in `/docs`
- **Examples**: Sample implementations in `/examples`
- **Research Papers**: Academic publications in `/research`
- **Discussion Forum**: GitHub Discussions for community support

### Getting Help
1. Check the troubleshooting section above
2. Review existing GitHub issues
3. Join the educational research community discussions
4. Contact the maintainers for academic collaboration

## Legal and Ethical Notice

This framework is designed exclusively for educational and legitimate security research purposes. Users are responsible for ensuring:

- All research activities comply with applicable laws and regulations
- Framework is used only in authorized environments
- Findings are disclosed responsibly through appropriate channels
- Research maintains educational and defensive security focus

Any use for malicious purposes or unauthorized network access is strictly prohibited and may violate applicable laws.