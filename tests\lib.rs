//! L2 Security Framework Test Infrastructure
//! Comprehensive testing framework for educational security analysis

pub mod unit_tests;
pub mod integration_tests; 
pub mod educational_tests;
pub mod audit_tests;
pub mod fixtures;
pub mod helpers;

// Re-export commonly used testing utilities
pub use fixtures::*;
pub use helpers::*;

use std::sync::Once;

static INIT: Once = Once::new();

/// Initialize test environment (called once per test run)
pub fn init_test_environment() {
    INIT.call_once(|| {
        env_logger::init();
        tokio::runtime::Runtime::new().unwrap();
    });
}

#[cfg(test)]
mod framework_tests {
    use super::*;
    
    #[test]
    fn test_framework_initialization() {
        init_test_environment();
        assert!(true, "Test framework initialized successfully");
    }
}