//! Unit Tests for L2 Security Framework Components
//! Tests individual modules in isolation

pub mod blast_yield_tests;
pub mod mantle_governance_tests;
pub mod mantle_governance_tests;
pub mod scroll_zkevm_tests;
pub mod optimism_bridge_tests;
pub mod arbitrum_rollup_tests;
pub mod ethereum_exploits_tests;
pub mod orchestrator_tests;

use crate::fixtures::*;
use crate::helpers::*;

#[cfg(test)]
mod framework_unit_tests {
    use super::*;
    use tokio::test;

    #[test]
    async fn test_mock_state_tracker_operations() {
        // Arrange
        let tracker = MockStateTracker::new();
        
        // Act
        tracker.record_operation("test_operation");
        tracker.increment_counter("exploit_attempts");
        tracker.set_flag("vulnerability_detected", true);
        
        // Assert
        assert_eq!(tracker.operation_count(), 1);
        assert_eq!(tracker.get_counter("exploit_attempts"), 1);
        assert!(tracker.is_flag_set("vulnerability_detected"));
    }

    #[test]
    async fn test_mock_config_builder() {
        // Arrange & Act
        let config = MockConfigBuilder::new()
            .with_yield_threshold(500_000_000_000_000_000)
            .with_overflow_trigger(5_000_000_000_000_000_000)
            .with_target_contract("0x1234567890123456789012345678901234567890")
            .with_simulation_enabled(false)
            .build();
        
        // Assert
        assert_eq!(config.yield_threshold, 500_000_000_000_000_000);
        assert_eq!(config.overflow_trigger, 5_000_000_000_000_000_000);
        assert_eq!(config.target_contract, "0x1234567890123456789012345678901234567890");
        assert!(!config.simulation_enabled);
    }

    #[test]
    async fn test_educational_scenario_descriptions() {
        // Arrange
        let scenarios = vec![
            EducationalScenario::BeginnerYieldFarming,
            EducationalScenario::IntermediateGovernanceAttack,
            EducationalScenario::AdvancedCrossChainExploit,
            EducationalScenario::ExpertZKProofManipulation,
        ];
        
        // Act & Assert
        for scenario in scenarios {
            let description = scenario.get_description();
            let objectives = scenario.get_learning_objectives();
            
            assert!(!description.is_empty());
            assert!(!objectives.is_empty());
            assert!(objectives.len() >= 2, "Each scenario should have at least 2 learning objectives");
        }
    }

    #[test]
    async fn test_performance_metrics_tracking() {
        // Arrange
        let mut metrics = PerformanceMetrics::new();
        
        // Act
        metrics.start_timing();
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        metrics.record_operation("test_operation");
        
        // Assert
        metrics.assert_operation_performance("test_operation", 50); // Should complete within 50ms
        let avg_time = metrics.average_operation_time();
        assert!(avg_time.as_millis() >= 10); // Should be at least 10ms due to our sleep
    }

    #[test]
    async fn test_test_result_aggregator() {
        // Arrange
        let mut aggregator = TestResultAggregator::new();
        
        // Act
        aggregator.record_test_pass();
        aggregator.record_test_pass();
        aggregator.record_test_fail();
        aggregator.record_vulnerability_detected();
        aggregator.record_educational_objective();
        aggregator.record_performance_benchmark();
        
        // Assert
        assert_eq!(aggregator.total_tests, 3);
        assert_eq!(aggregator.passed_tests, 2);
        assert_eq!(aggregator.failed_tests, 1);
        assert_eq!(aggregator.success_rate(), 66.66666666666667);
        assert_eq!(aggregator.security_vulnerabilities_detected, 1);
        
        let report = aggregator.generate_report();
        assert!(report.contains("Total Tests: 3"));
        assert!(report.contains("Security Vulnerabilities Detected: 1"));
    }
}