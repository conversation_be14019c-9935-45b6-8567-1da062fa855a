//! Test Fixtures for L2 Security Framework
//! Provides realistic test data for all security modules

use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Test configuration constants
pub const TEST_BLAST_CONTRACT: &str = "0x4300000000000000000000000000000000000001";
pub const TEST_MANTLE_GOVERNOR: &str = "0x7000000000000000000000000000000000000001";
pub const TEST_SCROLL_ZKEVM: &str = "0x5000000000000000000000000000000000000001";
pub const TEST_OPTIMISM_BRIDGE: &str = "******************************************";
pub const TEST_ARBITRUM_ROLLUP: &str = "******************************************";

/// Mock blast configuration for testing
#[derive(Debug, Clone)]
pub struct MockBlastConfig {
    pub yield_threshold: u128,
    pub overflow_trigger: u128,
    pub target_contract: String,
    pub simulation_enabled: bool,
}

impl Default for MockBlastConfig {
    fn default() -> Self {
        Self {
            yield_threshold: 1_000_000_000_000_000_000, // 1 ETH in wei
            overflow_trigger: 10_000_000_000_000_000_000, // 10 ETH
            target_contract: TEST_BLAST_CONTRACT.to_string(),
            simulation_enabled: true,
        }
    }
}

/// Mock governance attack vectors
#[derive(Debug, Clone)]
pub struct MockGovernanceAttack {
    pub proposal_id: u64,
    pub target_function: String,
    pub malicious_payload: Vec<u8>,
    pub voting_power_required: u128,
}

/// Mock cross-chain exploit data
#[derive(Debug, Clone)]
pub struct MockCrossChainExploit {
    pub source_chain: String,
    pub target_chain: String,
    pub bridge_contract: String,
    pub exploit_value: u128,
    pub vulnerability_type: String,
}

/// Test data factory for creating realistic exploit scenarios
pub struct TestDataFactory;

impl TestDataFactory {
    /// Generate blast yield overflow test scenarios
    pub fn create_blast_overflow_scenarios() -> Vec<MockBlastConfig> {
        vec![
            MockBlastConfig {
                yield_threshold: 500_000_000_000_000_000, // 0.5 ETH
                overflow_trigger: 5_000_000_000_000_000_000, // 5 ETH
                target_contract: TEST_BLAST_CONTRACT.to_string(),
                simulation_enabled: true,
            },
            MockBlastConfig {
                yield_threshold: 2_000_000_000_000_000_000, // 2 ETH
                overflow_trigger: 50_000_000_000_000_000_000, // 50 ETH
                target_contract: format!("0x{:040x}", 0x4300000000000000000000000000000000000002u128),
                simulation_enabled: false,
            },
        ]
    }

    /// Generate mantle governance attack scenarios
    pub fn create_governance_attack_scenarios() -> Vec<MockGovernanceAttack> {
        vec![
            MockGovernanceAttack {
                proposal_id: 12345,
                target_function: "emergencyWithdraw".to_string(),
                malicious_payload: vec![0x42, 0x13, 0x37, 0x69],
                voting_power_required: 1_000_000_000_000_000_000, // 1 voting token
            },
            MockGovernanceAttack {
                proposal_id: 67890,
                target_function: "updateTreasury".to_string(),
                malicious_payload: vec![0xde, 0xad, 0xbe, 0xef],
                voting_power_required: 10_000_000_000_000_000_000, // 10 voting tokens
            },
        ]
    }

    /// Generate cross-chain exploit scenarios
    pub fn create_cross_chain_scenarios() -> Vec<MockCrossChainExploit> {
        vec![
            MockCrossChainExploit {
                source_chain: "ethereum".to_string(),
                target_chain: "blast".to_string(),
                bridge_contract: TEST_OPTIMISM_BRIDGE.to_string(),
                exploit_value: 1_000_000_000_000_000_000, // 1 ETH
                vulnerability_type: "message_replay".to_string(),
            },
            MockCrossChainExploit {
                source_chain: "arbitrum".to_string(),
                target_chain: "scroll".to_string(),
                bridge_contract: TEST_ARBITRUM_ROLLUP.to_string(),
                exploit_value: 5_000_000_000_000_000_000, // 5 ETH
                vulnerability_type: "merkle_proof_manipulation".to_string(),
            },
        ]
    }

    /// Generate test transaction data
    pub fn create_test_transactions() -> HashMap<String, serde_json::Value> {
        let mut transactions = HashMap::new();
        
        transactions.insert(
            "blast_yield_deposit".to_string(),
            serde_json::json!({
                "hash": "******************************************",
                "from": "0x742d35Cc6734C0532925a3b8D0A9D2c6B");
    }
}

/// Mock async delay for realistic testing
pub async fn mock_network_delay() {
    tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
}

/// Create test result assertions
pub fn assert_exploit_detected(result: &Result<(), Box<dyn std::error::Error>>) {
    match result {
        Ok(_) => panic!("Expected exploit detection to fail, but it succeeded"),
        Err(e) => {
            let error_msg = e.to_string();
            assert!(
                error_msg.contains("vulnerability") || 
                error_msg.contains("exploit") || 
                error_msg.contains("attack"),
                "Error message should indicate security issue: {}", error_msg
            );
        }
    }
}

/// Educational test scenario markers
#[derive(Debug, Clone)]
pub enum EducationalScenario {
    BeginnerYieldFarming,
    IntermediateGovernanceAttack,
    AdvancedCrossChainExploit,
    ExpertZKProofManipulation,
}

impl EducationalScenario {
    pub fn get_description(&self) -> &'static str {
        match self {
            Self::BeginnerYieldFarming => "Basic yield farming overflow demonstration",
            Self::IntermediateGovernanceAttack => "Governance token manipulation attack",
            Self::AdvancedCrossChainExploit => "Cross-chain bridge vulnerability exploitation",
            Self::ExpertZKProofManipulation => "Zero-knowledge proof circuit manipulation",
        }
    }

    pub fn get_learning_objectives(&self) -> Vec<&'static str> {
        match self {
            Self::BeginnerYieldFarming => vec![
                "Understand integer overflow vulnerabilities",
                "Learn about yield calculation mechanisms",
                "Recognize unsafe arithmetic operations",
            ],
            Self::IntermediateGovernanceAttack => vec![
                "Analyze governance token economics",
                "Identify voting power concentration risks",
                "Understand proposal manipulation techniques",
            ],
            Self::AdvancedCrossChainExploit => vec![
                "Examine cross-chain message verification",
                "Analyze bridge security assumptions",
                "Understand merkle proof validation",
            ],
            Self::ExpertZKProofManipulation => vec![
                "Deep dive into ZK circuit constraints",
                "Analyze proof verification edge cases",
                "Understand trusted setup vulnerabilities",
            ],
        }
    }
}