//! Comprehensive test coverage for Mantle governance exploits
//! Tests all 2025 cross-chain attack vectors and vulnerability analysis patterns

use std::sync::Arc;
use tokio::time::Duration;
use crate::mantle_governance_exploits::*;

#[tokio::test]
async fn test_governance_double_spend_exploit() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    let result = exploiter.launch_governance_double_spend().await;
    assert!(result.is_ok(), "Governance double-spend exploit should execute successfully");
    
    // Verify exploit tracking
    let exploits = exploiter.active_double_spend_exploits.read().await;
    assert!(!exploits.is_empty(), "Should track double-spend exploits");
    
    // Verify governance tracker updates
    let tracker = exploiter.governance_tracker.lock().await;
    assert!(tracker.total_mnt_controlled > 0, "Should track controlled MNT tokens");
    assert!(tracker.successful_proposals > 0, "Should track successful proposals");
}

#[tokio::test]
async fn test_cross_chain_validation_bypass() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    let result = exploiter.launch_validation_bypass().await;
    assert!(result.is_ok(), "Validation bypass should execute successfully");
    
    // Verify bypass tracking
    let bypasses = exploiter.active_validation_bypasses.read().await;
    assert!(!bypasses.is_empty(), "Should track validation bypasses");
    
    // Verify bypass details
    let bypass = bypasses.values().next().unwrap();
    assert!(bypass.target_chains.len() >= 3, "Should target multiple chains");
    assert!(bypass.consensus_split_achieved, "Should achieve consensus split");
    assert!(bypass.total_voting_power_amplified > 0, "Should amplify voting power");
}

#[tokio::test]
async fn test_bridge_timing_attack() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    let result = exploiter.launch_bridge_timing_attack().await;
    assert!(result.is_ok(), "Bridge timing attack should execute successfully");
    
    // Verify attack tracking
    let attacks = exploiter.active_bridge_timing_attacks.read().await;
    assert!(!attacks.is_empty(), "Should track bridge timing attacks");
    
    // Verify attack details
    let attack = attacks.values().next().unwrap();
    assert!(attack.bridge_routes_exploited.len() >= 3, "Should exploit multiple bridge routes");
    assert!(attack.profit_from_timing > 0, "Should generate profit from timing");
    assert!(attack.coordination_success_rate > 0.5, "Should have reasonable success rate");
}

#[tokio::test]
async fn test_proposal_timing_manipulation() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    let result = exploiter.launch_proposal_timing_manipulation().await;
    assert!(result.is_ok(), "Proposal timing manipulation should execute successfully");
    
    // Verify manipulation tracking
    let manipulations = exploiter.active_proposal_manipulations.read().await;
    assert!(!manipulations.is_empty(), "Should track proposal manipulations");
    
    // Verify manipulation details
    let manipulation = manipulations.values().next().unwrap();
    assert!(manipulation.governance_takeover_percentage > 0.0, "Should achieve governance takeover");
    assert!(!manipulation.manipulation_techniques.is_empty(), "Should use manipulation techniques");
}

#[tokio::test]
async fn test_token_sync_exploit() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    let result = exploiter.launch_token_sync_exploit().await;
    assert!(result.is_ok(), "Token sync exploit should execute successfully");
    
    // Verify exploit tracking
    let exploits = exploiter.active_token_sync_exploits.read().await;
    assert!(!exploits.is_empty(), "Should track token sync exploits");
    
    // Verify exploit details
    let exploit = exploits.values().next().unwrap();
    assert!(exploit.desync_chains.len() >= 2, "Should desync multiple chains");
    assert!(exploit.total_tokens_duplicated > 0, "Should duplicate tokens");
    assert!(!exploit.double_spend_windows.is_empty(), "Should create double-spend windows");
}

#[tokio::test]
async fn test_delegation_exploit() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    let result = exploiter.launch_delegation_exploit().await;
    assert!(result.is_ok(), "Delegation exploit should execute successfully");
    
    // Verify exploit tracking
    let exploits = exploiter.active_delegation_exploits.read().await;
    assert!(!exploits.is_empty(), "Should track delegation exploits");
    
    // Verify exploit details
    let exploit = exploits.values().next().unwrap();
    assert!(exploit.delegation_power_concentrated > 0, "Should concentrate delegation power");
    assert!(!exploit.vote_buying_network.is_empty(), "Should establish vote buying network");
    assert!(exploit.plutocracy_score_achieved > 0.5, "Should achieve high plutocracy score");
}

#[tokio::test]
async fn test_multisig_bypass() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    let result = exploiter.launch_multisig_bypass().await;
    assert!(result.is_ok(), "Multi-sig bypass should execute successfully");
    
    // Verify bypass tracking
    let bypasses = exploiter.active_multisig_bypasses.read().await;
    assert!(!bypasses.is_empty(), "Should track multi-sig bypasses");
    
    // Verify bypass details
    let bypass = bypasses.values().next().unwrap();
    assert!(!bypass.target_treasury_addresses.is_empty(), "Should target treasury addresses");
    assert!(!bypass.funds_extracted_per_treasury.is_empty(), "Should extract funds");
    assert!(bypass.bypass_success_rate > 0.8, "Should have high success rate");
}

#[tokio::test]
async fn test_conversion_manipulation() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    let result = exploiter.launch_conversion_manipulation().await;
    assert!(result.is_ok(), "Conversion manipulation should execute successfully");
    
    // Verify manipulation tracking
    let manipulations = exploiter.active_conversion_manipulations.read().await;
    assert!(!manipulations.is_empty(), "Should track conversion manipulations");
    
    // Verify manipulation details
    let manipulation = manipulations.values().next().unwrap();
    assert!(manipulation.conversion_rate_after > manipulation.conversion_rate_before, 
        "Should increase conversion rate");
    assert!(manipulation.mnt_voting_power_gained > 0, "Should gain voting power");
    assert!(!manipulation.liquidity_pool_attacks.is_empty(), "Should attack liquidity pools");
}

#[tokio::test]
async fn test_dual_token_chaos() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    let result = exploiter.launch_dual_token_chaos().await;
    assert!(result.is_ok(), "Dual token chaos should execute successfully");
    
    // Verify chaos tracking
    let exploits = exploiter.active_dual_token_exploits.read().await;
    assert!(!exploits.is_empty(), "Should track dual token exploits");
    
    // Verify chaos details
    let exploit = exploits.values().next().unwrap();
    assert!(exploit.bit_tokens_accumulated > 0, "Should accumulate BIT tokens");
    assert!(exploit.mnt_price_manipulation > 1.0, "Should manipulate MNT price upward");
    assert!(exploit.profit_extracted > 0, "Should extract profit");
}

#[tokio::test]
async fn test_comprehensive_governance_report() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    // Execute several attacks to populate report data
    let _ = exploiter.launch_governance_double_spend().await;
    let _ = exploiter.launch_validation_bypass().await;
    let _ = exploiter.launch_dual_token_chaos().await;
    
    let report = exploiter.generate_governance_report().await;
    assert!(report.is_ok(), "Should generate governance report");
    
    let report_content = report.unwrap();
    assert!(report_content.contains("MANTLE GOVERNANCE EXPLOIT REPORT"), "Should contain report header");
    assert!(report_content.contains("PROFIT SUMMARY"), "Should contain profit summary");
    assert!(report_content.contains("GOVERNANCE DOUBLE-SPEND"), "Should contain double-spend section");
    assert!(report_content.contains("CROSS-CHAIN COORDINATION"), "Should contain coordination section");
}

#[tokio::test]
async fn test_governance_config_validation() {
    let config = GovernanceConfig::default();
    
    // Verify default configuration
    assert!(!config.mnt_token_address.is_empty(), "Should have MNT token address");
    assert!(!config.bit_token_address.is_empty(), "Should have BIT token address");
    assert!(!config.ethereum_governance.is_empty(), "Should have Ethereum governance address");
    assert!(!config.mantle_governance.is_empty(), "Should have Mantle governance address");
    assert!(!config.bsc_governance.is_empty(), "Should have BSC governance address");
    assert!(config.min_proposal_threshold > 0, "Should have proposal threshold");
    assert!(config.voting_period > 0, "Should have voting period");
}

#[tokio::test]
async fn test_exploiter_initialization() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config.clone());
    
    // Verify initialization
    assert_eq!(exploiter.config.mnt_token_address, config.mnt_token_address);
    
    // Verify empty collections
    let double_spend = exploiter.active_double_spend_exploits.read().await;
    assert!(double_spend.is_empty(), "Should start with empty double-spend exploits");
    
    let dual_token = exploiter.active_dual_token_exploits.read().await;
    assert!(dual_token.is_empty(), "Should start with empty dual token exploits");
    
    let validation = exploiter.active_validation_bypasses.read().await;
    assert!(validation.is_empty(), "Should start with empty validation bypasses");
}

#[tokio::test]
async fn test_price_manipulation_mechanics() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    let target_multiplier = 5.0;
    let orders = exploiter.execute_price_manipulation(target_multiplier).await;
    
    assert!(orders.is_ok(), "Price manipulation should execute successfully");
    let orders = orders.unwrap();
    assert!(orders.len() >= 10, "Should create multiple market orders");
    
    // Verify order details
    for order in &orders {
        assert!(order.amount > 0, "Orders should have positive amounts");
        assert!(order.price > 0.0, "Orders should have positive prices");
        assert_eq!(order.order_type, "BUY", "Should be buy orders");
        assert_eq!(order.token_pair, "MNT/ETH", "Should target MNT/ETH pair");
    }
}

#[tokio::test]
async fn test_token_dump_mechanics() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    let mnt_amount = 1000000 * 1e18 as u128; // 1M MNT tokens
    let profit = exploiter.execute_token_dump(mnt_amount).await;
    
    assert!(profit.is_ok(), "Token dump should execute successfully");
    let profit = profit.unwrap();
    assert!(profit > 0, "Should generate positive profit");
    assert!(profit < mnt_amount, "Profit should be reasonable compared to input");
}

#[tokio::test]
async fn test_attack_coordination_timing() {
    let config = GovernanceConfig::default();
    let exploiter = MantleGovernanceExploiter::new(config);
    
    let start_time = std::time::Instant::now();
    
    // Execute multiple attacks in sequence
    let _ = exploiter.launch_governance_double_spend().await;
    let _ = exploiter.launch_validation_bypass().await;
    let _ = exploiter.launch_bridge_timing_attack().await;
    
    let elapsed = start_time.elapsed();
    
    // Verify timing constraints
    assert!(elapsed < Duration::from_secs(60), "Attacks should complete within reasonable time");
    
    // Verify chain coordinator state
    let coordinator = exploiter.chain_coordinator.read().await;
    assert!(coordinator.ethereum_connection, "Should establish Ethereum connection");
    assert!(coordinator.mantle_connection, "Should establish Mantle connection");
    assert!(coordinator.bsc_connection, "Should establish BSC connection");
}

#[test]
fn test_exploit_data_structures() {
    // Test GovernanceDoubleSpendExploit
    let double_spend = GovernanceDoubleSpendExploit {
        exploit_id: "test".to_string(),
        mnt_token_amount: 1000,
        chains_exploited: vec!["Ethereum".to_string()],
        votes_cast: 3,
        proposal_id: "PROP_123".to_string(),
        treasury_drained: 5000,
        attack_timestamp: 1234567890,
    };
    assert_eq!(double_spend.votes_cast, 3);
    
    // Test CrossChainValidationBypass
    let validation_bypass = CrossChainValidationBypass {
        exploit_id: "test".to_string(),
        target_chains: vec!["Ethereum".to_string(), "Mantle".to_string()],
        validation_delays_exploited: std::collections::HashMap::new(),
        finality_assumptions_violated: vec!["Test assumption".to_string()],
        consensus_split_achieved: true,
        validator_set_manipulation: false,
        total_voting_power_amplified: 10000,
        bypass_timestamp: 1234567890,
    };
    assert!(validation_bypass.consensus_split_achieved);
    
    // Test DelegationExploit
    let delegation_exploit = DelegationExploit {
        exploit_id: "test".to_string(),
        target_delegates: vec!["delegate1".to_string()],
        delegation_power_concentrated: 50000,
        vote_buying_network: std::collections::HashMap::new(),
        delegation_caps_bypassed: true,
        quadratic_voting_circumvented: false,
        plutocracy_score_achieved: 0.85,
        network_effect_multiplier: 2.5,
    };
    assert!(delegation_exploit.delegation_caps_bypassed);
}