# API Documentation

## Overview

This document provides comprehensive API documentation for all security modules in the Layer 2 Blockchain Security Research Framework. All APIs are designed for educational and legitimate security research purposes.

## Core Framework API

### L2SecurityOrchestrator

The main orchestrator class that coordinates security analysis across all Layer 2 networks.

#### Constructor

```rust
impl L2SecurityOrchestrator {
    pub fn new() -> Self
}
```

**Description**: Creates a new instance of the security orchestrator with default configurations for all network analyzers.

**Returns**: `L2SecurityOrchestrator` - Fully initialized orchestrator instance

**Example**:
```rust
use l2_security_framework::L2SecurityOrchestrator;

let orchestrator = L2SecurityOrchestrator::new();
```

#### Core Analysis Methods

##### `launch_comprehensive_security_analysis`

```rust
pub async fn launch_comprehensive_security_analysis(&self) -> Result<()>
```

**Description**: Executes complete security analysis across all supported Layer 2 networks in five phases.

**Returns**: `Result<()>` - Success or error result

**Analysis Phases**:
1. Blast Network Security Analysis
2. Mantle Network Security Analysis  
3. Scroll Network Security Analysis
4. Cross-L2 Security Analysis
5. Comprehensive Report Generation

**Example**:
```rust
#[tokio::main]
async fn main() -> Result<()> {
    let orchestrator = L2SecurityOrchestrator::new();
    orchestrator.launch_comprehensive_security_analysis().await?;
    Ok(())
}
```

##### Individual Network Analysis

```rust
async fn analyze_blast_network(&self) -> Result<()>
async fn analyze_mantle_network(&self) -> Result<()>
async fn analyze_scroll_network(&self) -> Result<()>
async fn analyze_cross_l2_vectors(&self) -> Result<()>
```

**Description**: Individual network-specific security analysis methods.

**Usage**: Call directly for focused analysis on specific networks.

**Example**:
```rust
// Analyze only Blast network
orchestrator.analyze_blast_network().await?;
```

## 🔥 Blast Network APIs

### BlastYieldExploiter

Analyzes yield generation mechanisms for potential overflow and manipulation vulnerabilities.

#### Configuration

```rust
pub struct BlastConfig {
    pub yield_rate_threshold: f64,
    pub overflow_detection: bool,
    pub compound_frequency: Duration,
    pub max_yield_multiplier: f64,
}

impl Default for BlastConfig {
    fn default() -> Self {
        Self {
            yield_rate_threshold: 0.05,
            overflow_detection: true,
            compound_frequency: Duration::from_secs(86400), // 24 hours
            max_yield_multiplier: 10.0,
        }
    }
}
```

#### Constructor

```rust
impl BlastYieldExploiter {
    pub fn new(config: BlastConfig) -> Self
}
```

#### Methods

##### `execute_yield_overflow_attack`

```rust
pub async fn execute_yield_overflow_attack(&self, attack_id: String) -> Result<YieldAnalysisResult>
```

**Parameters**:
- `attack_id: String` - Unique identifier for the analysis session

**Returns**: `Result<YieldAnalysisResult>` - Comprehensive yield vulnerability assessment

**Analysis Components**:
- Overflow vulnerability detection
- Compound interest manipulation assessment
- Economic impact calculation
- Mitigation recommendations

**Example**:
```rust
let config = BlastConfig::default();
let analyzer = BlastYieldExploiter::new(config);
let attack_id = "yield_analysis_001".to_string();
let results = analyzer.execute_yield_overflow_attack(attack_id).await?;
```

### BlastTimingAttacker

Analyzes timing vulnerabilities in sequencer operations and MEV opportunities.

#### Configuration

```rust
pub struct TimingConfig {
    pub timing_window_ms: u64,
    pub mev_threshold: f64,
    pub block_time_variance: f64,
    pub sequencer_delays: Vec<Duration>,
}
```

#### Methods

##### `execute_timing_attack`

```rust
pub async fn execute_timing_attack(&self, attack_id: String) -> Result<TimingAnalysisResult>
```

**Analysis Areas**:
- Sequencer timing patterns
- MEV extraction opportunities
- Front-running vulnerabilities
- Sandwich attack vectors

### BlastPointsExploiter

Evaluates points system security and gaming vulnerabilities.

#### Configuration

```rust
pub struct PointsConfig {
    pub points_multiplier: f64,
    pub sybil_detection: bool,
    pub wash_trading_threshold: f64,
    pub gaming_patterns: Vec<String>,
}
```

#### Methods

##### `execute_points_exploit`

```rust
pub async fn execute_points_exploit(&self, attack_id: String) -> Result<PointsAnalysisResult>
```

**Vulnerability Categories**:
- Sybil attack detection
- Wash trading patterns
- Point farming strategies
- Collusion network analysis

## 🏔️ Mantle Network APIs

### MantleGovernanceExploiter

Analyzes governance security patterns and dual-token vulnerabilities.

#### Configuration

```rust
pub struct GovernanceConfig {
    pub voting_threshold: f64,
    pub proposal_delay: Duration,
    pub execution_delay: Duration,
    pub quorum_percentage: f64,
    pub token_distribution_analysis: bool,
}

impl Default for GovernanceConfig {
    fn default() -> Self {
        Self {
            voting_threshold: 0.04, // 4% threshold
            proposal_delay: Duration::from_secs(172800), // 48 hours
            execution_delay: Duration::from_secs(259200), // 72 hours
            quorum_percentage: 0.10, // 10% quorum
            token_distribution_analysis: true,
        }
    }
}
```

#### Methods

##### `execute_governance_attack`

```rust
pub async fn execute_governance_attack(&self, attack_id: String) -> Result<GovernanceAnalysisResult>
```

**Security Assessment Areas**:
- Token concentration analysis
- Vote buying market detection
- Proposal manipulation vectors
- Emergency governance security

**Return Structure**:
```rust
pub struct GovernanceAnalysisResult {
    pub attack_id: String,
    pub token_distribution: TokenDistribution,
    pub voting_patterns: Vec<VotingPattern>,
    pub centralization_score: f64,
    pub risk_level: RiskLevel,
    pub vulnerabilities: Vec<GovernanceVulnerability>,
    pub recommendations: Vec<String>,
}
```

### MantleOPStackExploiter

Analyzes OP Stack implementation and EigenDA integration security.

#### Methods

##### `exploit_eigenDA`

```rust
pub async fn exploit_eigenDA(&self, exploit_id: String) -> Result<OPStackAnalysisResult>
```

**Analysis Components**:
- EigenDA integration vulnerabilities
- Data availability attacks
- Validator set security
- Slashing condition exploits

### MantleTokenAnalyzer

Examines MNT token economics and bridge security.

#### Methods

##### `analyze_token_security`

```rust
pub async fn analyze_token_security(&self, analysis_id: String) -> Result<TokenAnalysisResult>
```

**Security Areas**:
- Token bridge vulnerabilities
- Staking mechanism security
- Reward distribution analysis
- Economic attack vectors

## 📜 Scroll Network APIs

### ScrollZkEvmExploiter

Analyzes zkEVM circuit implementation and zero-knowledge proof security.

#### Configuration

```rust
pub struct ZkEvmConfig {
    pub circuit_verification: bool,
    pub proof_validation: bool,
    pub soundness_checks: bool,
    pub constraint_analysis: bool,
}
```

#### Methods

##### `execute_opcode_translation_attack`

```rust
pub async fn execute_opcode_translation_attack(&self, attack_id: String) -> Result<ZkEvmAnalysisResult>
```

**Analysis Areas**:
- Opcode translation accuracy
- Circuit constraint violations
- Proof generation vulnerabilities
- Verification bypass attempts

**Return Structure**:
```rust
pub struct ZkEvmAnalysisResult {
    pub attack_id: String,
    pub circuit_vulnerabilities: Vec<CircuitVulnerability>,
    pub proof_system_analysis: ProofSystemAnalysis,
    pub soundness_violations: Vec<SoundnessViolation>,
    pub recommendations: Vec<SecurityRecommendation>,
}
```

### ScrollProverAnalyzer

Evaluates prover network coordination and consensus security.

#### Methods

##### `analyze_prover_coordination`

```rust
pub async fn analyze_prover_coordination(&self) -> Result<ProverAnalysisResult>
```

**Coordination Analysis**:
- Prover network health metrics
- Coordination vulnerability detection
- Economic incentive analysis
- Collusion pattern identification

## 🌐 Cross-Chain APIs

### CrossL2VampireExploiter

Analyzes cross-chain bridge security and vampire attack vectors.

#### Configuration

```rust
pub struct VampireConfig {
    pub target_chains: Vec<String>,
    pub bridge_protocols: Vec<String>,
    pub liquidity_threshold: f64,
    pub attack_duration: Duration,
}
```

#### Methods

##### `execute_vampire_attack`

```rust
pub async fn execute_vampire_attack(&self, attack_id: String) -> Result<VampireAttackResult>
```

**Attack Vector Analysis**:
- Bridge validator security
- Message passing vulnerabilities
- Liquidity extraction patterns
- Cross-chain arbitrage opportunities

**Return Structure**:
```rust
pub struct VampireAttackResult {
    pub attack_id: String,
    pub bridges_analyzed: Vec<BridgeAnalysis>,
    pub liquidity_impact: LiquidityImpact,
    pub arbitrage_opportunities: Vec<ArbitrageOpportunity>,
    pub systemic_risks: Vec<SystemicRisk>,
    pub total_vulnerability_score: f64,
}
```

## Common Data Structures

### Risk Assessment Types

```rust
#[derive(Debug, Clone, PartialEq)]
pub enum RiskLevel {
    Low,      // 1.0-3.9
    Medium,   // 4.0-6.9
    High,     // 7.0-8.9
    Critical, // 9.0-10.0
}

pub struct SecurityFinding {
    pub finding_id: String,
    pub category: String,
    pub description: String,
    pub severity: String,
    pub recommendation: String,
    pub cvss_score: Option<f64>,
}
```

### Vulnerability Classification

```rust
pub enum VulnerabilityClass {
    Economic,     // Financial and incentive-based attacks
    Technical,    // Implementation and protocol bugs
    Governance,   // Governance and consensus attacks
    Cryptographic, // Cryptographic and mathematical flaws
    Operational,  // Operational and maintenance issues
}

pub struct Vulnerability {
    pub id: String,
    pub class: VulnerabilityClass,
    pub severity: RiskLevel,
    pub description: String,
    pub impact: String,
    pub likelihood: f64,
    pub mitigation: String,
}
```

### Configuration Management

```rust
pub trait AnalysisConfig {
    fn validate(&self) -> Result<()>;
    fn merge(&mut self, other: Self);
    fn to_json(&self) -> String;
    fn from_json(json: &str) -> Result<Self> where Self: Sized;
}
```

## Error Handling

### Error Types

```rust
#[derive(Debug, thiserror::Error)]
pub enum FrameworkError {
    #[error("Configuration error: {0}")]
    ConfigError(String),
    
    #[error("Network analysis failed: {0}")]
    NetworkError(String),
    
    #[error("Vulnerability detection error: {0}")]
    VulnerabilityError(String),
    
    #[error("Data serialization error: {0}")]
    SerializationError(String),
    
    #[error("External service error: {0}")]
    ExternalError(String),
}

pub type Result<T> = std::result::Result<T, FrameworkError>;
```

### Error Handling Best Practices

```rust
// Proper error handling example
async fn analyze_with_error_handling() -> Result<()> {
    let orchestrator = L2SecurityOrchestrator::new();
    
    match orchestrator.analyze_blast_network().await {
        Ok(_) => println!("✅ Blast analysis completed successfully"),
        Err(FrameworkError::NetworkError(msg)) => {
            eprintln!("❌ Network analysis failed: {}", msg);
            // Implement retry logic or fallback
        },
        Err(e) => {
            eprintln!("❌ Unexpected error: {}", e);
            return Err(e);
        }
    }
    
    Ok(())
}
```

## Advanced Usage Patterns

### Custom Analysis Workflows

```rust
// Custom analysis workflow example
use l2_security_framework::*;

pub struct CustomAnalysisWorkflow {
    orchestrator: L2SecurityOrchestrator,
    custom_configs: HashMap<String, Box<dyn AnalysisConfig>>,
}

impl CustomAnalysisWorkflow {
    pub fn new() -> Self {
        Self {
            orchestrator: L2SecurityOrchestrator::new(),
            custom_configs: HashMap::new(),
        }
    }
    
    pub async fn execute_targeted_analysis(&self, targets: Vec<&str>) -> Result<()> {
        for target in targets {
            match target {
                "blast_yield" => {
                    let analyzer = &self.orchestrator.blast_yield_exploiter;
                    let results = analyzer.execute_yield_overflow_attack(
                        format!("targeted_blast_{}", chrono::Utc::now().timestamp())
                    ).await?;
                    self.process_results(results).await?;
                },
                "mantle_governance" => {
                    let analyzer = &self.orchestrator.mantle_governance_exploiter;
                    let results = analyzer.execute_governance_attack(
                        format!("targeted_mantle_{}", chrono::Utc::now().timestamp())
                    ).await?;
                    self.process_results(results).await?;
                },
                _ => println!("⚠️ Unknown target: {}", target),
            }
        }
        Ok(())
    }
    
    async fn process_results<T>(&self, results: T) -> Result<()> 
    where T: serde::Serialize {
        // Custom result processing logic
        Ok(())
    }
}
```

### Parallel Analysis Execution

```rust
// Parallel execution example
use tokio::task::JoinHandle;

async fn parallel_network_analysis() -> Result<()> {
    let orchestrator = Arc::new(L2SecurityOrchestrator::new());
    
    let blast_handle: JoinHandle<Result<()>> = {
        let orch = Arc::clone(&orchestrator);
        tokio::spawn(async move {
            orch.analyze_blast_network().await
        })
    };
    
    let mantle_handle: JoinHandle<Result<()>> = {
        let orch = Arc::clone(&orchestrator);
        tokio::spawn(async move {
            orch.analyze_mantle_network().await
        })
    };
    
    let scroll_handle: JoinHandle<Result<()>> = {
        let orch = Arc::clone(&orchestrator);
        tokio::spawn(async move {
            orch.analyze_scroll_network().await
        })
    };
    
    // Wait for all analyses to complete
    let (blast_result, mantle_result, scroll_result) = tokio::try_join!(
        blast_handle,
        mantle_handle,
        scroll_handle
    )?;
    
    blast_result?;
    mantle_result?;
    scroll_result?;
    
    println!("✅ All parallel analyses completed successfully");
    Ok(())
}
```

## Integration Examples

### Database Integration

```rust
// Database integration example
use sqlx::{sqlite::SqlitePool, Row};

pub struct DatabaseIntegration {
    pool: SqlitePool,
}

impl DatabaseIntegration {
    pub async fn new(database_url: &str) -> Result<Self> {
        let pool = SqlitePool::connect(database_url).await?;
        Ok(Self { pool })
    }
    
    pub async fn store_vulnerability(&self, vuln: &Vulnerability) -> Result<()> {
        sqlx::query!(
            "INSERT INTO vulnerabilities (id, class, severity, description) VALUES (?, ?, ?, ?)",
            vuln.id,
            vuln.class as i32,
            vuln.severity as i32,
            vuln.description
        )
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }
    
    pub async fn get_vulnerabilities_by_severity(&self, severity: RiskLevel) -> Result<Vec<Vulnerability>> {
        let rows = sqlx::query!(
            "SELECT * FROM vulnerabilities WHERE severity = ?",
            severity as i32
        )
        .fetch_all(&self.pool)
        .await?;
        
        // Convert rows to Vulnerability structs
        // Implementation details omitted for brevity
        Ok(vec![])
    }
}
```

### Logging and Monitoring Integration

```rust
// Comprehensive logging example
use tracing::{info, warn, error, instrument};
use tracing_subscriber;

#[instrument]
pub async fn instrumented_analysis() -> Result<()> {
    tracing_subscriber::fmt::init();
    
    info!("🚀 Starting instrumented security analysis");
    
    let orchestrator = L2SecurityOrchestrator::new();
    
    match orchestrator.launch_comprehensive_security_analysis().await {
        Ok(_) => {
            info!("✅ Security analysis completed successfully");
        },
        Err(e) => {
            error!("❌ Security analysis failed: {}", e);
            return Err(e);
        }
    }
    
    Ok(())
}
```

## Educational Research Guidelines

### API Usage for Academic Research

```rust
// Academic research example
pub struct AcademicResearchSession {
    session_id: String,
    research_purpose: String,
    institution: String,
    orchestrator: L2SecurityOrchestrator,
}

impl AcademicResearchSession {
    pub fn new(purpose: String, institution: String) -> Self {
        Self {
            session_id: format!("academic_{}", chrono::Utc::now().timestamp()),
            research_purpose: purpose,
            institution,
            orchestrator: L2SecurityOrchestrator::new(),
        }
    }
    
    #[instrument(fields(session_id = %self.session_id, institution = %self.institution))]
    pub async fn conduct_research(&self) -> Result<ResearchReport> {
        info!("🎓 Beginning academic research session");
        info!("📋 Purpose: {}", self.research_purpose);
        info!("🏛️ Institution: {}", self.institution);
        
        // Execute educational analysis
        self.orchestrator.launch_comprehensive_security_analysis().await?;
        
        // Generate academic report
        let report = ResearchReport {
            session_id: self.session_id.clone(),
            research_purpose: self.research_purpose.clone(),
            institution: self.institution.clone(),
            findings: self.generate_educational_findings().await?,
            methodology: self.document_methodology(),
            ethical_compliance: true,
        };
        
        info!("📚 Academic research session completed");
        Ok(report)
    }
    
    async fn generate_educational_findings(&self) -> Result<Vec<EducationalFinding>> {
        // Implementation for educational finding generation
        Ok(vec![])
    }
    
    fn document_methodology(&self) -> ResearchMethodology {
        ResearchMethodology {
            framework_version: env!("CARGO_PKG_VERSION").to_string(),
            analysis_date: chrono::Utc::now(),
            ethical_approval: true,
            controlled_environment: true,
            defensive_focus: true,
        }
    }
}
```

## Security and Compliance

### Ethical Usage Enforcement

```rust
// Ethical usage validation
pub struct EthicalUsageValidator {
    approved_purposes: Vec<String>,
    restricted_networks: Vec<String>,
}

impl EthicalUsageValidator {
    pub fn validate_research_purpose(&self, purpose: &str) -> Result<()> {
        let ethical_keywords = ["educational", "academic", "defensive", "research", "learning"];
        let unethical_keywords = ["exploit", "profit", "attack", "hack", "steal"];
        
        if unethical_keywords.iter().any(|keyword| purpose.to_lowercase().contains(keyword)) {
            return Err(FrameworkError::ConfigError(
                "Research purpose contains unethical keywords".to_string()
            ));
        }
        
        if !ethical_keywords.iter().any(|keyword| purpose.to_lowercase().contains(keyword)) {
            return Err(FrameworkError::ConfigError(
                "Research purpose must clearly indicate educational intent".to_string()
            ));
        }
        
        Ok(())
    }
    
    pub fn validate_network_target(&self, network: &str) -> Result<()> {
        if self.restricted_networks.contains(&network.to_string()) {
            return Err(FrameworkError::ConfigError(
                format!("Network {} is restricted for this research type", network)
            ));
        }
        Ok(())
    }
}
```

This API documentation provides comprehensive coverage of all framework components while maintaining focus on educational and legitimate security research applications. All examples emphasize ethical usage, proper error handling, and academic research standards.