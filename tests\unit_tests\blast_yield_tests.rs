//! Unit Tests for Blast Yield Overflow Exploiter
//! Tests the core blast yield farming vulnerability detection

use crate::fixtures::*;
use crate::helpers::*;
use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use tokio::test;

// Mock BlastConfig for testing
#[derive(Debug, Clone)]
struct BlastConfig {
    pub yield_threshold: u128,
    pub overflow_trigger: u128,
    pub target_contract: String,
    pub simulation_enabled: bool,
}

impl Default for BlastConfig {
    fn default() -> Self {
        Self {
            yield_threshold: 1_000_000_000_000_000_000, // 1 ETH
            overflow_trigger: 10_000_000_000_000_000_000, // 10 ETH
            target_contract: TEST_BLAST_CONTRACT.to_string(),
            simulation_enabled: true,
        }
    }
}

// Mock BlastYieldExploiter for isolated testing
struct MockBlastYieldExploiter {
    config: BlastConfig,
    profit_tracker: Arc<Mutex<HashMap<String, u128>>>,
    yield_tracker: Arc<Mutex<HashMap<String, u128>>>,
    exploit_attempts: Arc<Mutex<u32>>,
}

impl MockBlastYieldExploiter {
    fn new(config: BlastConfig) -> Self {
        Self {
            config,
            profit_tracker: Arc::new(Mutex::new(HashMap::new())),
            yield_tracker: Arc::new(Mutex::new(HashMap::new())),
            exploit_attempts: Arc::new(Mutex::new(0)),
        }
    }

    async fn launch_overflow_attack(&self) -> Result<(), Box<dyn std::error::Error>> {
        // Increment attempt counter
        {
            let mut attempts = self.exploit_attempts.lock().unwrap();
            *attempts += 1;
        }

        // Simulate vulnerability detection
        if self.config.yield_threshold > self.config.overflow_trigger {
            return Err("Overflow vulnerability detected: yield_threshold exceeds overflow_trigger".into());
        }

        // Simulate normal operation
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        
        // Record profit tracking
        {
            let mut profits = self.profit_tracker.lock().unwrap();
            profits.insert("attack_1".to_string(), 1_000_000_000_000_000_000);
        }

        Ok(())
    }

    async fn analyze_yield_mechanics(&self) -> Result<(), Box<dyn std::error::Error>> {
        // Simulate analysis delay
        mock_network_delay().await;

        // Check for suspicious yield calculations
        if self.config.yield_threshold < 100_000_000_000_000_000 { // Less than 0.1 ETH
            return Err("Suspiciously low yield threshold detected".into());
        }

        // Record yield analysis
        {
            let mut yields = self.yield_tracker.lock().unwrap();
            yields.insert("analysis_1".to_string(), self.config.yield_threshold);
        }

        Ok(())
    }

    fn get_exploit_attempts(&self) -> u32 {
        *self.exploit_attempts.lock().unwrap()
    }

    fn get_profit_total(&self) -> u128 {
        self.profit_tracker.lock().unwrap().values().sum()
    }
}

#[cfg(test)]
mod blast_yield_unit_tests {
    use super::*;

    #[test]
    async fn test_blast_exploiter_creation_with_default_config() {
        // Arrange
        let config = BlastConfig::default();
        
        // Act
        let exploiter = MockBlastYieldExploiter::new(config.clone());
        
        // Assert
        assert_eq!(exploiter.config.yield_threshold, 1_000_000_000_000_000_000);
        assert_eq!(exploiter.config.overflow_trigger, 10_000_000_000_000_000_000);
        assert_eq!(exploiter.config.target_contract, TEST_BLAST_CONTRACT);
        assert!(exploiter.config.simulation_enabled);
    }

    #[test]
    async fn test_blast_exploiter_creation_with_custom_config() {
        // Arrange
        let config = BlastConfig {
            yield_threshold: 500_000_000_000_000_000, // 0.5 ETH
            overflow_trigger: 5_000_000_000_000_000_000, // 5 ETH
            target_contract: "******************************************".to_string(),
            simulation_enabled: false,
        };
        
        // Act
        let exploiter = MockBlastYieldExploiter::new(config.clone());
        
        // Assert
        assert_eq!(exploiter.config.yield_threshold, 500_000_000_000_000_000);
        assert_eq!(exploiter.config.overflow_trigger, 5_000_000_000_000_000_000);
        assert_eq!(exploiter.config.target_contract, "******************************************");
        assert!(!exploiter.config.simulation_enabled);
    }

    #[test]
    async fn test_launch_overflow_attack_with_safe_configuration() {
        // Arrange
        let config = BlastConfig::default(); // Safe configuration
        let exploiter = MockBlastYieldExploiter::new(config);
        
        // Act
        let result = exploiter.launch_overflow_attack().await;
        
        // Assert
        assert!(result.is_ok(), "Safe configuration should not trigger overflow detection");
        assert_eq!(exploiter.get_exploit_attempts(), 1);
        assert_eq!(exploiter.get_profit_total(), 1_000_000_000_000_000_000);
    }

    #[test]
    async fn test_launch_overflow_attack_detects_vulnerability() {
        // Arrange
        let config = BlastConfig {
            yield_threshold: 15_000_000_000_000_000_000, // 15 ETH
            overflow_trigger: 10_000_000_000_000_000_000, // 10 ETH
            target_contract: TEST_BLAST_CONTRACT.to_string(),
            simulation_enabled: true,
        };
        let exploiter = MockBlastYieldExploiter::new(config);
        
        // Act
        let result = exploiter.launch_overflow_attack().await;
        
        // Assert
        SecurityAssertions::assert_exploit_detected(&result);
        assert_eq!(exploiter.get_exploit_attempts(), 1);
        assert_eq!(exploiter.get_profit_total(), 0); // No profit on detected vulnerability
    }

    #[test]
    async fn test_analyze_yield_mechanics_with_normal_threshold() {
        // Arrange
        let config = BlastConfig::default();
        let exploiter = MockBlastYieldExploiter::new(config);
        
        // Act
        let result = exploiter.analyze_yield_mechanics().await;
        
        // Assert
        assert!(result.is_ok(), "Normal yield threshold should pass analysis");
    }

    #[test]
    async fn test_analyze_yield_mechanics_detects_suspicious_threshold() {
        // Arrange
        let config = BlastConfig {
            yield_threshold: 50_000_000_000_000_000, // 0.05 ETH (suspiciously low)
            overflow_trigger: 10_000_000_000_000_000_000,
            target_contract: TEST_BLAST_CONTRACT.to_string(),
            simulation_enabled: true,
        };
        let exploiter = MockBlastYieldExploiter::new(config);
        
        // Act
        let result = exploiter.analyze_yield_mechanics().await;
        
        // Assert
        assert!(result.is_err(), "Suspiciously low threshold should be detected");
        let error_msg = result.unwrap_err().to_string();
        assert!(error_msg.contains("Suspiciously low yield threshold"));
    }

    #[test]
    async fn test_multiple_attack_attempts_tracking() {
        // Arrange
        let config = BlastConfig::default();
        let exploiter = MockBlastYieldExploiter::new(config);
        
        // Act
        for _ in 0..3 {
            let _ = exploiter.launch_overflow_attack().await;
        }
        
        // Assert
        assert_eq!(exploiter.get_exploit_attempts(), 3);
        assert_eq!(exploiter.get_profit_total(), 3_000_000_000_000_000_000); // 3 ETH total
    }

    #[test]
    async fn test_performance_requirements() {
        // Arrange
        let config = BlastConfig::default();
        let exploiter = MockBlastYieldExploiter::new(config);
        let mut metrics = PerformanceMetrics::new();
        
        // Act
        metrics.start_timing();
        let _ = exploiter.launch_overflow_attack().await;
        metrics.record_operation("launch_overflow_attack");
        
        // Assert
        metrics.assert_operation_performance("launch_overflow_attack", 100); // Should complete in <100ms
    }

    #[test]
    async fn test_concurrent_attack_attempts() {
        // Arrange
        let config = BlastConfig::default();
        let exploiter = Arc::new(MockBlastYieldExploiter::new(config));
        
        // Act
        let mut handles = vec![];
        for _ in 0..5 {
            let exploiter_clone = Arc::clone(&exploiter);
            let handle = tokio::spawn(async move {
                exploiter_clone.launch_overflow_attack().await
            });
            handles.push(handle);
        }
        
        // Wait for all tasks to complete
        for handle in handles {
            let _ = handle.await.unwrap();
        }
        
        // Assert
        assert_eq!(exploiter.get_exploit_attempts(), 5);
        assert_eq!(exploiter.get_profit_total(), 5_000_000_000_000_000_000); // 5 ETH total
    }

    #[test]
    async fn test_educational_scenario_beginner_yield_farming() {
        // Arrange
        let scenario = EducationalScenario::BeginnerYieldFarming;
        let config = BlastConfig::default();
        let exploiter = MockBlastYieldExploiter::new(config);
        let tracker = MockStateTracker::new();
        
        // Act - Simulate educational objectives completion
        tracker.set_flag("understand_integer_overflow_vulnerabilities", true);
        tracker.set_flag("learn_about_yield_calculation_mechanisms", true);
        tracker.set_flag("recognize_unsafe_arithmetic_operations", true);
        
        let _ = exploiter.launch_overflow_attack().await;
        
        // Assert
        SecurityAssertions::assert_educational_objectives_met(&tracker, &scenario);
        assert_eq!(scenario.get_description(), "Basic yield farming overflow demonstration");
        assert_eq!(scenario.get_learning_objectives().len(), 3);
    }

    #[test]
    async fn test_blast_config_validation() {
        // Test various configuration edge cases
        let test_cases = vec![
            (0, 1_000_000_000_000_000_000, false), // Zero threshold
            (u128::MAX, 1_000_000_000_000_000_000, true), // Max threshold triggers overflow
            (1_000_000_000_000_000_000, 0, true), // Zero overflow trigger
            (1, 1, false), // Minimal values
        ];
        
        for (yield_threshold, overflow_trigger, should_detect_vulnerability) in test_cases {
            // Arrange
            let config = BlastConfig {
                yield_threshold,
                overflow_trigger,
                target_contract: TEST_BLAST_CONTRACT.to_string(),
                simulation_enabled: true,
            };
            let exploiter = MockBlastYieldExploiter::new(config);
            
            // Act
            let result = exploiter.launch_overflow_attack().await;
            
            // Assert
            if should_detect_vulnerability {
                assert!(result.is_err(), "Configuration should detect vulnerability for threshold: {}, trigger: {}", yield_threshold, overflow_trigger);
            } else {
                assert!(result.is_ok(), "Configuration should be safe for threshold: {}, trigger: {}", yield_threshold, overflow_trigger);
            }
        }
    }

    #[test]
    async fn test_auto_compound_frequency_detection() {
        // Arrange
        let config = BlastConfig::default();
        let exploiter = MockBlastYieldExploiter::new(config);
        
        // Test different compound frequencies
        let test_frequencies = vec![1, 10, 100, 1000]; // blocks
        
        for frequency in test_frequencies {
            // Act - Simulate compound frequency analysis
            let is_suspicious = frequency < 100; // Less than 100 blocks is suspicious
            
            // Assert
            if is_suspicious {
                assert!(frequency < 100, "Frequency {} should be flagged as suspicious", frequency);
            } else {
                assert!(frequency >= 100, "Frequency {} should be considered normal", frequency);
            }
        }
    }

    #[test]
    async fn test_rebase_timing_prediction() {
        // Arrange
        let config = BlastConfig::default();
        let exploiter = MockBlastYieldExploiter::new(config);
        
        // Test rebase timing prediction accuracy
        let current_time = 1640995200u64; // Mock timestamp
        let standard_interval = 28800u64; // 8 hours
        
        // Act
        let predicted_rebase = current_time + standard_interval;
        let actual_rebase = current_time + 28850u64; // 50 seconds off
        let timing_accuracy = ((predicted_rebase as i64 - actual_rebase as i64).abs() as f64) / standard_interval as f64;
        
        // Assert
        assert!(timing_accuracy < 0.01, "Timing prediction should be within 1% accuracy, got: {}", timing_accuracy);
    }

    #[test]
    async fn test_cross_domain_yield_rate_divergence() {
        // Arrange
        let l1_yield_rate = 0.04f64; // 4% APY
        let l2_yield_rate = 0.12f64; // 12% APY
        
        // Act
        let rate_divergence = l2_yield_rate - l1_yield_rate;
        let arbitrage_opportunity = rate_divergence > 0.05; // More than 5% difference
        
        // Assert
        assert!(arbitrage_opportunity, "Should detect arbitrage opportunity with {}% divergence", rate_divergence * 100.0);
        assert!(rate_divergence > 0.05, "Rate divergence should exceed threshold");
    }

    #[test]
    async fn test_yield_token_reentrancy_depth_limits() {
        // Arrange
        let config = BlastConfig::default();
        let exploiter = MockBlastYieldExploiter::new(config);
        
        // Test various reentrancy depths
        let test_depths = vec![1, 5, 10, 15, 20];
        
        for depth in test_depths {
            // Act
            let gas_cost_estimate = depth * 21000; // Basic gas per call
            let is_feasible = gas_cost_estimate < 10_000_000; // Gas limit check
            
            // Assert
            if depth <= 15 {
                assert!(is_feasible, "Reentrancy depth {} should be feasible", depth);
            } else {
                // Deeper reentrancy may hit gas limits
                println!("Reentrancy depth {} gas cost: {}", depth, gas_cost_estimate);
            }
        }
    }

    #[test]
    async fn test_mev_extraction_calculations() {
        // Arrange
        let front_run_amount = 100_000 * 1e18 as u128; // 100K ETH
        let yield_rate = 0.05f64; // 5% APY
        
        // Act
        let daily_yield = (front_run_amount as f64 * yield_rate / 365.0) as u128;
        let mev_profit = daily_yield / 2; // 50% MEV extraction
        
        // Assert
        assert!(mev_profit > 0, "MEV profit should be positive");
        assert!(mev_profit < daily_yield, "MEV profit should not exceed daily yield");
        
        let mev_eth = mev_profit as f64 / 1e18;
        assert!(mev_eth > 10.0, "MEV extraction should exceed 10 ETH for large front-run");
    }

    #[test]
    async fn test_comprehensive_attack_sequence_validation() {
        // Arrange
        let config = BlastConfig::default();
        let exploiter = MockBlastYieldExploiter::new(config);
        
        // Act - Simulate complete attack sequence
        let attack_phases = vec![
            "overflow_detection",
            "death_spiral_initiation",
            "auto_compound_manipulation",
            "rebase_timing_exploitation",
            "cross_domain_arbitrage",
            "yield_token_reentrancy"
        ];
        
        let mut phase_results = Vec::new();
        for phase in &attack_phases {
            // Simulate each phase execution
            let success = match *phase {
                "overflow_detection" => true,
                "death_spiral_initiation" => true,
                "auto_compound_manipulation" => true,
                "rebase_timing_exploitation" => true,
                "cross_domain_arbitrage" => true,
                "yield_token_reentrancy" => true,
                _ => false,
            };
            phase_results.push((*phase, success));
        }
        
        // Assert
        assert_eq!(phase_results.len(), 6, "Should execute all 6 attack phases");
        
        let successful_phases = phase_results.iter().filter(|(_, success)| *success).count();
        assert_eq!(successful_phases, 6, "All attack phases should execute successfully");
        
        // Validate phase order
        assert_eq!(phase_results[0].0, "overflow_detection");
        assert_eq!(phase_results[5].0, "yield_token_reentrancy");
    }

    #[test]
    async fn test_profit_tracking_accuracy() {
        // Arrange
        let config = BlastConfig::default();
        let exploiter = MockBlastYieldExploiter::new(config);
        
        // Simulate profits from different attack vectors
        let overflow_profit = 500_000_000 * 1e18 as u128; // 500M ETH
        let death_spiral_profit = 200_000_000 * 1e18 as u128; // 200M ETH
        let auto_compound_profit = 300_000_000 * 1e18 as u128; // 300M ETH
        let rebase_timing_profit = 150_000_000 * 1e18 as u128; // 150M ETH
        let cross_domain_profit = 400_000_000 * 1e18 as u128; // 400M ETH
        let reentrancy_profit = 250_000_000 * 1e18 as u128; // 250M ETH
        
        // Act
        let total_profit = overflow_profit + death_spiral_profit + auto_compound_profit
                         + rebase_timing_profit + cross_domain_profit + reentrancy_profit;
        
        // Assert
        assert_eq!(total_profit, 1_800_000_000 * 1e18 as u128, "Total profit should equal 1.8B ETH");
        
        // Validate individual attack profitability
        assert!(overflow_profit > 0, "Overflow attack should be profitable");
        assert!(cross_domain_profit > auto_compound_profit, "Cross-domain should be most profitable single attack");
    }
}