//! Test Helper Functions for L2 Security Framework
//! Utilities for async testing, assertions, and mock setup

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio::time::{Duration, timeout};

/// Async test wrapper with timeout protection
pub async fn with_timeout<T, F>(future: F, timeout_ms: u64) -> Result<T, &'static str>
where
    F: std::future::Future<Output = T>,
{
    timeout(Duration::from_millis(timeout_ms), future)
        .await
        .map_err(|_| "Test timed out")
}

/// Mock state tracker for testing async operations
#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct MockStateTracker {
    pub operations: Arc<Mutex<Vec<String>>>,
    pub counters: Arc<Mutex<HashMap<String, u64>>>,
    pub flags: Arc<Mutex<HashMap<String, bool>>>,
}

impl MockStateTracker {
    pub fn new() -> Self {
        Self::default()
    }

    /// Record an operation for verification
    pub fn record_operation(&self, operation: &str) {
        let mut ops = self.operations.lock().unwrap();
        ops.push(operation.to_string());
    }

    /// Increment a counter
    pub fn increment_counter(&self, key: &str) {
        let mut counters = self.counters.lock().unwrap();
        *counters.entry(key.to_string()).or_insert(0) += 1;
    }

    /// Set a flag
    pub fn set_flag(&self, key: &str, value: bool) {
        let mut flags = self.flags.lock().unwrap();
        flags.insert(key.to_string(), value);
    }

    /// Get operation count
    pub fn operation_count(&self) -> usize {
        self.operations.lock().unwrap().len()
    }

    /// Get counter value
    pub fn get_counter(&self, key: &str) -> u64 {
        self.counters.lock().unwrap().get(key).copied().unwrap_or(0)
    }

    /// Check if flag is set
    pub fn is_flag_set(&self, key: &str) -> bool {
        self.flags.lock().unwrap().get(key).copied().unwrap_or(false)
    }

    /// Get all operations
    pub fn get_operations(&self) -> Vec<String> {
        self.operations.lock().unwrap().clone()
    }
}

/// Mock configuration builder for consistent test setups
pub struct MockConfigBuilder {
    yield_threshold: Option<u128>,
    overflow_trigger: Option<u128>,
    target_contract: Option<String>,
    simulation_enabled: Option<bool>,
}

impl MockConfigBuilder {
    pub fn new() -> Self {
        Self {
            yield_threshold: None,
            overflow_trigger: None,
            target_contract: None,
            simulation_enabled: None,
        }
    }

    pub fn with_yield_threshold(mut self, threshold: u128) -> Self {
        self.yield_threshold = Some(threshold);
        self
    }

    pub fn with_overflow_trigger(mut self, trigger: u128) -> Self {
        self.overflow_trigger = Some(trigger);
        self
    }

    pub fn with_target_contract(mut self, contract: &str) -> Self {
        self.target_contract = Some(contract.to_string());
        self
    }

    pub fn with_simulation_enabled(mut self, enabled: bool) -> Self {
        self.simulation_enabled = Some(enabled);
        self
    }

    pub fn build(self) -> crate::fixtures::MockBlastConfig {
        crate::fixtures::MockBlastConfig {
            yield_threshold: self.yield_threshold.unwrap_or(1_000_000_000_000_000_000),
            overflow_trigger: self.overflow_trigger.unwrap_or(10_000_000_000_000_000_000),
            target_contract: self.target_contract.unwrap_or_else(|| 
                crate::fixtures::TEST_BLAST_CONTRACT.to_string()
            ),
            simulation_enabled: self.simulation_enabled.unwrap_or(true),
        }
    }
}

impl Default for MockConfigBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// Assertion helpers for security testing
pub struct SecurityAssertions;

impl SecurityAssertions {
    /// Assert that an exploit was detected and properly handled
    pub fn assert_exploit_detected(result: &Result<(), Box<dyn std::error::Error>>) {
        match result {
            Ok(_) => panic!("Expected security vulnerability to be detected"),
            Err(e) => {
                let error_msg = e.to_string().to_lowercase();
                assert!(
                    error_msg.contains("vulnerability") ||
                    error_msg.contains("exploit") ||
                    error_msg.contains("attack") ||
                    error_msg.contains("overflow") ||
                    error_msg.contains("malicious"),
                    "Error should indicate security issue: {}", e
                );
            }
        }
    }

    /// Assert that a governance attack was prevented
    pub fn assert_governance_protection(result: &Result<(), Box<dyn std::error::Error>>) {
        match result {
            Ok(_) => panic!("Expected governance attack to be blocked"),
            Err(e) => {
                let error_msg = e.to_string().to_lowercase();
                assert!(
                    error_msg.contains("governance") ||
                    error_msg.contains("proposal") ||
                    error_msg.contains("voting") ||
                    error_msg.contains("unauthorized"),
                    "Error should indicate governance protection: {}", e
                );
            }
        }
    }

    /// Assert cross-chain security validation
    pub fn assert_cross_chain_validation(
        operations: &[String],
        expected_validations: &[&str],
    ) {
        for validation in expected_validations {
            assert!(
                operations.iter().any(|op| op.contains(validation)),
                "Missing cross-chain validation: {}. Operations: {:?}",
                validation,
                operations
            );
        }
    }

    /// Assert educational scenario completion
    pub fn assert_educational_objectives_met(
        tracker: &MockStateTracker,
        scenario: &crate::fixtures::EducationalScenario,
    ) {
        let objectives = scenario.get_learning_objectives();
        for objective in objectives {
            let objective_key = objective.replace(" ", "_").to_lowercase();
            assert!(
                tracker.is_flag_set(&objective_key),
                "Educational objective not completed: {}",
                objective
            );
        }
    }
}

/// Performance testing utilities
pub struct PerformanceMetrics {
    start_time: Option<std::time::Instant>,
    operations: Vec<(String, Duration)>,
}

impl PerformanceMetrics {
    pub fn new() -> Self {
        Self {
            start_time: None,
            operations: Vec::new(),
        }
    }

    /// Start timing an operation
    pub fn start_timing(&mut self) {
        self.start_time = Some(std::time::Instant::now());
    }

    /// Record operation completion
    pub fn record_operation(&mut self, name: &str) {
        if let Some(start) = self.start_time.take() {
            let duration = start.elapsed();
            self.operations.push((name.to_string(), duration));
        }
    }

    /// Assert operation completed within time limit
    pub fn assert_operation_performance(&self, operation: &str, max_ms: u64) {
        if let Some((_, duration)) = self.operations.iter()
            .find(|(name, _)| name == operation) {
            assert!(
                duration.as_millis() <= max_ms as u128,
                "Operation '{}' took {}ms, expected <{}ms",
                operation,
                duration.as_millis(),
                max_ms
            );
        } else {
            panic!("Operation '{}' not found in metrics", operation);
        }
    }

    /// Get average operation time
    pub fn average_operation_time(&self) -> Duration {
        if self.operations.is_empty() {
            return Duration::from_millis(0);
        }
        
        let total: Duration = self.operations.iter()
            .map(|(_, duration)| *duration)
            .sum();
        
        total / self.operations.len() as u32
    }
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self::new()
    }
}

/// Test result aggregator for comprehensive reporting
#[derive(Debug, Default)]
pub struct TestResultAggregator {
    pub total_tests: u32,
    pub passed_tests: u32,
    pub failed_tests: u32,
    pub security_vulnerabilities_detected: u32,
    pub educational_objectives_completed: u32,
    pub performance_benchmarks_met: u32,
}

impl TestResultAggregator {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn record_test_pass(&mut self) {
        self.total_tests += 1;
        self.passed_tests += 1;
    }

    pub fn record_test_fail(&mut self) {
        self.total_tests += 1;
        self.failed_tests += 1;
    }

    pub fn record_vulnerability_detected(&mut self) {
        self.security_vulnerabilities_detected += 1;
    }

    pub fn record_educational_objective(&mut self) {
        self.educational_objectives_completed += 1;
    }

    pub fn record_performance_benchmark(&mut self) {
        self.performance_benchmarks_met += 1;
    }

    pub fn success_rate(&self) -> f64 {
        if self.total_tests == 0 {
            return 0.0;
        }
        (self.passed_tests as f64) / (self.total_tests as f64) * 100.0
    }

    pub fn generate_report(&self) -> String {
        format!(
            "Test Results Summary:\n\
             Total Tests: {}\n\
             Passed: {} ({:.1}%)\n\
             Failed: {}\n\
             Security Vulnerabilities Detected: {}\n\
             Educational Objectives Completed: {}\n\
             Performance Benchmarks Met: {}",
            self.total_tests,
            self.passed_tests,
            self.success_rate(),
            self.failed_tests,
            self.security_vulnerabilities_detected,
            self.educational_objectives_completed,
            self.performance_benchmarks_met
        )
    }
}